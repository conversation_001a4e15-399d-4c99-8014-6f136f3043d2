-- Initialize YouTube Spam Detection Database

-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS youtube_spam_detector;

-- Create user if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'username') THEN
        CREATE USER username WITH PASSWORD 'password';
    END IF;
END
$$;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE youtube_spam_detector TO username;

-- Connect to the database
\c youtube_spam_detector;

-- Grant schema privileges
GRANT ALL ON SCHEMA public TO username;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO username;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO username;

-- Insert default spam keywords
INSERT INTO spam_keywords (keyword, category, weight, is_active) VALUES
-- Gambling keywords
('casino', 'gambling', 1.0, true),
('poker', 'gambling', 1.0, true),
('blackjack', 'gambling', 1.0, true),
('roulette', 'gambling', 1.0, true),
('slots', 'gambling', 1.0, true),
('betting', 'gambling', 0.8, true),
('gamble', 'gambling', 1.0, true),
('jackpot', 'gambling', 0.9, true),
('lottery', 'gambling', 0.7, true),
('scratch card', 'gambling', 0.8, true),
('bingo', 'gambling', 0.6, true),
('sports bet', 'gambling', 0.9, true),
('odds', 'gambling', 0.5, true),

-- Financial scam keywords
('win money', 'scam', 0.9, true),
('easy money', 'scam', 0.9, true),
('quick cash', 'scam', 0.9, true),
('guaranteed win', 'scam', 1.0, true),
('instant loan', 'loan', 0.9, true),
('no credit check', 'loan', 0.8, true),
('guaranteed approval', 'loan', 0.9, true),
('cash advance', 'loan', 0.7, true),
('payday loan', 'loan', 0.8, true),
('quick loan', 'loan', 0.8, true),
('emergency cash', 'loan', 0.7, true),
('bad credit ok', 'loan', 0.8, true),

-- Cryptocurrency gambling
('crypto casino', 'gambling', 1.0, true),
('bitcoin casino', 'gambling', 1.0, true),
('eth gambling', 'gambling', 1.0, true),
('crypto betting', 'gambling', 1.0, true),

-- Investment scams
('get rich quick', 'scam', 1.0, true),
('investment opportunity', 'scam', 0.7, true),
('double your money', 'scam', 1.0, true),
('guaranteed returns', 'scam', 0.9, true),
('risk free', 'scam', 0.8, true),
('passive income', 'scam', 0.6, true),

-- Phishing indicators
('click here', 'phishing', 0.6, true),
('limited time', 'phishing', 0.5, true),
('act now', 'phishing', 0.6, true),
('dont miss out', 'phishing', 0.5, true),
('exclusive offer', 'phishing', 0.6, true),
('secret method', 'scam', 0.8, true),
('insider tip', 'scam', 0.7, true)
ON CONFLICT (keyword) DO NOTHING;
