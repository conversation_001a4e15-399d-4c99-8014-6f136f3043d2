from sqlalchemy import Column, Inte<PERSON>, String, DateTime, Boolean, Text, Float, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import Base

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    google_id = Column(String, unique=True, index=True)
    name = Column(String, nullable=False)
    avatar_url = Column(String)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    subscription_tier = Column(String, default="free")  # free, premium, enterprise
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    video_analyses = relationship("VideoAnalysis", back_populates="user")
    comment_analyses = relationship("CommentAnalysis", back_populates="user")

class VideoAnalysis(Base):
    __tablename__ = "video_analyses"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    video_id = Column(String, nullable=False, index=True)
    video_title = Column(String)
    video_url = Column(String)
    channel_name = Column(String)
    channel_id = Column(String)
    total_comments = Column(Integer, default=0)
    spam_comments_found = Column(Integer, default=0)
    comments_deleted = Column(Integer, default=0)
    analysis_status = Column(String, default="pending")  # pending, processing, completed, failed
    dry_run = Column(Boolean, default=True)
    max_results = Column(Integer, default=100)
    confidence_threshold = Column(Float, default=0.7)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True))
    
    # Relationships
    user = relationship("User", back_populates="video_analyses")
    comment_analyses = relationship("CommentAnalysis", back_populates="video_analysis")

class CommentAnalysis(Base):
    __tablename__ = "comment_analyses"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    video_analysis_id = Column(Integer, ForeignKey("video_analyses.id"))
    comment_id = Column(String, nullable=False, index=True)
    comment_text = Column(Text, nullable=False)
    author_name = Column(String)
    author_channel_id = Column(String)
    like_count = Column(Integer, default=0)
    reply_count = Column(Integer, default=0)
    published_at = Column(DateTime(timezone=True))
    
    # Spam detection results
    is_spam = Column(Boolean, default=False)
    spam_confidence = Column(Float, default=0.0)
    spam_categories = Column(JSON)  # ["gambling", "phishing", "scam", etc.]
    detection_method = Column(String)  # "keyword", "ml_model", "hybrid"
    
    # Action taken
    action_taken = Column(String)  # "none", "flagged", "deleted"
    deleted_at = Column(DateTime(timezone=True))
    deletion_success = Column(Boolean)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="comment_analyses")
    video_analysis = relationship("VideoAnalysis", back_populates="comment_analyses")

class SpamKeyword(Base):
    __tablename__ = "spam_keywords"
    
    id = Column(Integer, primary_key=True, index=True)
    keyword = Column(String, nullable=False, index=True)
    category = Column(String, nullable=False)  # gambling, phishing, scam, etc.
    weight = Column(Float, default=1.0)
    is_active = Column(Boolean, default=True)
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class AnalysisLog(Base):
    __tablename__ = "analysis_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    action = Column(String, nullable=False)  # analyze_video, analyze_comment, delete_comment
    resource_id = Column(String)  # video_id or comment_id
    status = Column(String, nullable=False)  # success, error, warning
    message = Column(Text)
    log_metadata = Column(JSON)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
