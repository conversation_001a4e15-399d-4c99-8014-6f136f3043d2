import re
import json
import string
from typing import Dict, List
from datetime import datetime
from collections import Counter
import unicodedata

class SpamDetector:
    def __init__(self):
        self.gambling_keywords = [
            # English gambling terms
            'casino', 'poker', 'blackjack', 'roulette', 'slots', 'betting', 'gamble',
            'jackpot', 'lottery', 'scratch card', 'bingo', 'sports bet', 'odds',
            'win money', 'easy money', 'quick cash', 'guaranteed win',
            
            # Cryptocurrency gambling
            'crypto casino', 'bitcoin casino', 'eth gambling', 'crypto betting',
            'stake.com', 'roobet', 'duelbits', 'rollbit',
            
            # Common gambling sites
            'bet365', 'pokerstars', 'fanduel', 'draftkings', 'bovada',
            
            # Loan/financial scams
            'instant loan', 'no credit check', 'guaranteed approval', 'cash advance',
            'payday loan', 'quick loan', 'emergency cash', 'bad credit ok',
            
            # Investment scams
            'get rich quick', 'investment opportunity', 'double your money',
            'guaranteed returns', 'risk free', 'passive income',
            
            # General scam indicators
            'click here', 'limited time', 'act now', 'dont miss out',
            'exclusive offer', 'secret method', 'insider tip'
        ]
        
        self.phishing_patterns = [
            r'bit\.ly\/\w+',
            r'tinyurl\.com\/\w+',
            r'goo\.gl\/\w+',
            r'free\s+money',
            r'click\s+here\s+to\s+claim',
            r'verify\s+your\s+account',
            r'suspended\s+account',
            r'urgent\s+action\s+required'
        ]
        
        self.spam_indicators = [
            r'[🎰🎲🃏💰💸💵💴💶💷🤑💎🎯🔥⚡]',  # Gambling emojis
            r'(\w)\1{3,}',  # Repeated characters (aaaa, bbbb)
            r'[A-Z]{5,}',   # All caps words
            r'www\.\w+\.\w+',  # URLs
            r'https?://[^\s]+',  # HTTP URLs
            r'\d{10,}',     # Long numbers (phone numbers)
            r'[^\w\s]{3,}',  # Multiple special characters
            r'\b\w*[0-9]+\w*[0-9]+\w*\b',  # Mixed numbers and letters (l33t speak)
        ]

        # Advanced spam patterns
        self.advanced_patterns = [
            r'(?i)\b(telegram|whatsapp|discord)\s*[@:]?\s*[@\w]+',  # Social media contacts
            r'(?i)\b(dm|pm|message)\s+me\b',  # Direct message requests
            r'(?i)\b\d+%\s*(profit|return|guaranteed)',  # Percentage promises
            r'(?i)\b(no\s+risk|risk\s+free|100%\s+safe)',  # Risk-free claims
            r'(?i)\b(limited\s+time|expires\s+soon|hurry)',  # Urgency tactics
        ]
    
    async def analyze_comment(self, comment_text: str) -> Dict:
        """Analyze a comment for spam indicators"""
        # Preprocess text
        cleaned_text = self._preprocess_text(comment_text)
        comment_lower = cleaned_text.lower()

        # Initialize result
        result = {
            'is_spam': False,
            'confidence': 0.0,
            'categories': [],
            'method': 'hybrid',
            'details': {}
        }

        # Check for gambling keywords
        gambling_score = self._check_gambling_keywords(comment_lower)
        if gambling_score > 0:
            result['categories'].append('gambling')
            result['confidence'] += gambling_score * 0.35

        # Check for phishing patterns
        phishing_score = self._check_phishing_patterns(comment_text)
        if phishing_score > 0:
            result['categories'].append('phishing')
            result['confidence'] += phishing_score * 0.25

        # Check for general spam indicators
        spam_score = self._check_spam_indicators(comment_text)
        if spam_score > 0:
            result['categories'].append('spam')
            result['confidence'] += spam_score * 0.15

        # Check for loan/financial scams
        loan_score = self._check_loan_scams(comment_lower)
        if loan_score > 0:
            result['categories'].append('loan')
            result['confidence'] += loan_score * 0.25

        # Check advanced patterns
        advanced_score = self._check_advanced_patterns(comment_text)
        if advanced_score > 0:
            result['categories'].append('advanced_spam')
            result['confidence'] += advanced_score * 0.2

        # Check text characteristics
        char_score = self._check_text_characteristics(comment_text)
        result['confidence'] += char_score * 0.1

        # Normalize confidence score
        result['confidence'] = min(result['confidence'], 1.0)

        # Determine if spam based on confidence threshold
        result['is_spam'] = result['confidence'] >= 0.5

        # Add details
        result['details'] = {
            'gambling_score': gambling_score,
            'phishing_score': phishing_score,
            'spam_score': spam_score,
            'loan_score': loan_score,
            'advanced_score': advanced_score,
            'char_score': char_score,
            'text_length': len(comment_text),
            'cleaned_length': len(cleaned_text),
            'has_urls': bool(re.search(r'http[s]?://|www\.', comment_text)),
            'has_emojis': bool(re.search(r'[🎰🎲🃏💰💸💵💴💶💷🤑💎🎯🔥⚡]', comment_text)),
            'has_social_contacts': bool(re.search(r'(?i)\b(telegram|whatsapp|discord)\s*[@:]?\s*[@\w]+', comment_text)),
            'suspicious_chars': self._count_suspicious_characters(comment_text)
        }

        return result
    
    def _check_gambling_keywords(self, text: str) -> float:
        """Check for gambling-related keywords"""
        matches = 0
        total_keywords = len(self.gambling_keywords)
        
        for keyword in self.gambling_keywords:
            if keyword in text:
                matches += 1
        
        return matches / total_keywords if total_keywords > 0 else 0
    
    def _check_phishing_patterns(self, text: str) -> float:
        """Check for phishing patterns"""
        matches = 0
        
        for pattern in self.phishing_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                matches += 1
        
        return min(matches / len(self.phishing_patterns), 1.0)
    
    def _check_spam_indicators(self, text: str) -> float:
        """Check for general spam indicators"""
        score = 0
        
        for pattern in self.spam_indicators:
            matches = len(re.findall(pattern, text))
            if matches > 0:
                score += min(matches * 0.2, 0.5)
        
        return min(score, 1.0)
    
    def _check_loan_scams(self, text: str) -> float:
        """Check for loan/financial scam keywords"""
        loan_keywords = [
            'instant loan', 'no credit check', 'guaranteed approval',
            'cash advance', 'payday loan', 'quick loan', 'emergency cash',
            'bad credit ok', 'apply now', 'get approved'
        ]
        
        matches = 0
        for keyword in loan_keywords:
            if keyword in text:
                matches += 1
        
        return matches / len(loan_keywords) if loan_keywords else 0

    def _preprocess_text(self, text: str) -> str:
        """Preprocess text to normalize unicode and remove excessive whitespace"""
        # Normalize unicode characters
        text = unicodedata.normalize('NFKD', text)

        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text).strip()

        # Replace common character substitutions used to evade detection
        substitutions = {
            '0': 'o', '1': 'i', '3': 'e', '4': 'a', '5': 's',
            '7': 't', '8': 'b', '@': 'a', '$': 's'
        }

        normalized = text
        for char, replacement in substitutions.items():
            normalized = normalized.replace(char, replacement)

        return normalized

    def _check_advanced_patterns(self, text: str) -> float:
        """Check for advanced spam patterns"""
        matches = 0

        for pattern in self.advanced_patterns:
            if re.search(pattern, text):
                matches += 1

        return min(matches / len(self.advanced_patterns), 1.0)

    def _check_text_characteristics(self, text: str) -> float:
        """Check text characteristics that indicate spam"""
        score = 0

        # Check for excessive capitalization
        if len(text) > 10:
            caps_ratio = sum(1 for c in text if c.isupper()) / len(text)
            if caps_ratio > 0.5:
                score += 0.3

        # Check for excessive punctuation
        punct_count = sum(1 for c in text if c in string.punctuation)
        if len(text) > 0:
            punct_ratio = punct_count / len(text)
            if punct_ratio > 0.2:
                score += 0.2

        # Check for repeated words
        words = text.lower().split()
        if len(words) > 3:
            word_counts = Counter(words)
            max_count = max(word_counts.values())
            if max_count > len(words) * 0.3:
                score += 0.2

        # Check for very short or very long comments
        if len(text) < 10 or len(text) > 1000:
            score += 0.1

        return min(score, 1.0)

    def _count_suspicious_characters(self, text: str) -> Dict[str, int]:
        """Count suspicious characters and patterns"""
        return {
            'emojis': len(re.findall(r'[🎰🎲🃏💰💸💵💴💶💷🤑💎🎯🔥⚡]', text)),
            'urls': len(re.findall(r'https?://[^\s]+|www\.\w+\.\w+', text)),
            'phone_numbers': len(re.findall(r'\d{10,}', text)),
            'repeated_chars': len(re.findall(r'(\w)\1{3,}', text)),
            'caps_words': len(re.findall(r'\b[A-Z]{3,}\b', text)),
            'special_chars': len(re.findall(r'[^\w\s]{2,}', text))
        }
    
    async def batch_analyze_comments(self, comments: List[str]) -> List[Dict]:
        """Analyze multiple comments in batch"""
        results = []
        
        for comment in comments:
            result = await self.analyze_comment(comment)
            results.append(result)
        
        return results
    
    def get_spam_statistics(self, results: List[Dict]) -> Dict:
        """Get statistics from batch analysis results"""
        total_comments = len(results)
        spam_comments = sum(1 for r in results if r['is_spam'])
        
        categories = {}
        for result in results:
            for category in result['categories']:
                categories[category] = categories.get(category, 0) + 1
        
        avg_confidence = sum(r['confidence'] for r in results) / total_comments if total_comments > 0 else 0
        
        return {
            'total_comments': total_comments,
            'spam_comments': spam_comments,
            'spam_rate': spam_comments / total_comments if total_comments > 0 else 0,
            'categories': categories,
            'average_confidence': avg_confidence
        }
