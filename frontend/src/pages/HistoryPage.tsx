import React, { useState, useEffect } from 'react';
import { videoService } from '../services/api';
import { LoadingSpinner } from '../components/LoadingSpinner';
import {
  VideoCameraIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

interface VideoAnalysis {
  id: number;
  video_id: string;
  video_title: string;
  video_url: string;
  channel_name: string;
  total_comments: number;
  spam_comments_found: number;
  comments_deleted: number;
  analysis_status: string;
  dry_run: boolean;
  created_at: string;
  completed_at?: string;
}

export const HistoryPage: React.FC = () => {
  const [analyses, setAnalyses] = useState<VideoAnalysis[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  const loadAnalyses = async (page = 0) => {
    try {
      const data = await videoService.getUserVideoAnalyses(page * 20, 20);
      
      if (page === 0) {
        setAnalyses(data);
      } else {
        setAnalyses(prev => [...prev, ...data]);
      }
      
      setHasMore(data.length === 20);
      setCurrentPage(page);
    } catch (error) {
      console.error('Failed to load analyses:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadAnalyses();
  }, []);

  const loadMore = () => {
    if (!isLoading && hasMore) {
      loadAnalyses(currentPage + 1);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'processing':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return 'badge-success';
      case 'failed':
        return 'badge-danger';
      case 'processing':
        return 'badge-warning';
      default:
        return 'badge-info';
    }
  };

  if (isLoading && analyses.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Analysis History</h1>
        <p className="mt-1 text-sm text-gray-600">
          View all your past video analyses and their results.
        </p>
      </div>

      {/* Analyses List */}
      {analyses.length === 0 ? (
        <div className="text-center py-12">
          <VideoCameraIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No analyses yet</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by analyzing your first video.
          </p>
          <div className="mt-6">
            <a href="/analyze" className="btn btn-primary">
              Analyze Video
            </a>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {analyses.map((analysis) => (
            <div key={analysis.id} className="card">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-2">
                    {getStatusIcon(analysis.analysis_status)}
                    <h3 className="text-lg font-medium text-gray-900 truncate">
                      {analysis.video_title || `Video ${analysis.video_id}`}
                    </h3>
                    <span className={`badge ${getStatusBadge(analysis.analysis_status)}`}>
                      {analysis.analysis_status}
                    </span>
                  </div>
                  
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600">
                      <strong>Channel:</strong> {analysis.channel_name}
                    </p>
                    <p className="text-sm text-gray-600">
                      <strong>Video ID:</strong> {analysis.video_id}
                    </p>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>
                        <strong>Comments:</strong> {analysis.total_comments}
                      </span>
                      <span>
                        <strong>Spam Found:</strong> {analysis.spam_comments_found}
                      </span>
                      <span>
                        <strong>Deleted:</strong> {analysis.comments_deleted}
                      </span>
                      {analysis.dry_run && (
                        <span className="badge badge-info">Dry Run</span>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>
                        Started: {new Date(analysis.created_at).toLocaleString()}
                      </span>
                      {analysis.completed_at && (
                        <span>
                          Completed: {new Date(analysis.completed_at).toLocaleString()}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="ml-4 flex-shrink-0">
                  <a
                    href={analysis.video_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn btn-secondary text-sm"
                  >
                    View Video
                  </a>
                </div>
              </div>
              
              {/* Progress Bar for Spam Detection Rate */}
              {analysis.total_comments > 0 && (
                <div className="mt-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Spam Detection Rate</span>
                    <span className="font-medium">
                      {((analysis.spam_comments_found / analysis.total_comments) * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="mt-1 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-red-500 h-2 rounded-full"
                      style={{
                        width: `${(analysis.spam_comments_found / analysis.total_comments) * 100}%`,
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
          ))}
          
          {/* Load More Button */}
          {hasMore && (
            <div className="text-center">
              <button
                onClick={loadMore}
                disabled={isLoading}
                className="btn btn-secondary flex items-center space-x-2 mx-auto"
              >
                {isLoading ? (
                  <>
                    <LoadingSpinner size="sm" />
                    <span>Loading...</span>
                  </>
                ) : (
                  <span>Load More</span>
                )}
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
