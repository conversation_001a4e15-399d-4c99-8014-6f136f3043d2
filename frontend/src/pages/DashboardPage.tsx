import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { userService, videoService } from '../services/api';
import { LoadingSpinner } from '../components/LoadingSpinner';
import {
  VideoCameraIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

interface UserStats {
  total_videos_analyzed: number;
  total_comments_analyzed: number;
  total_spam_detected: number;
  total_comments_deleted: number;
  success_rate: number;
}

interface RecentAnalysis {
  id: number;
  video_id: string;
  video_title: string;
  total_comments: number;
  spam_comments_found: number;
  analysis_status: string;
  created_at: string;
}

export const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<UserStats | null>(null);
  const [recentAnalyses, setRecentAnalyses] = useState<RecentAnalysis[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const [statsData, analysesData] = await Promise.all([
          userService.getUserStats(),
          videoService.getUserVideoAnalyses(0, 5),
        ]);
        
        setStats(statsData);
        setRecentAnalyses(analysesData);
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  const statCards = [
    {
      name: 'Videos Analyzed',
      value: stats?.total_videos_analyzed || 0,
      icon: VideoCameraIcon,
      color: 'bg-blue-500',
    },
    {
      name: 'Comments Analyzed',
      value: stats?.total_comments_analyzed || 0,
      icon: ChartBarIcon,
      color: 'bg-green-500',
    },
    {
      name: 'Spam Detected',
      value: stats?.total_spam_detected || 0,
      icon: ExclamationTriangleIcon,
      color: 'bg-red-500',
    },
    {
      name: 'Comments Deleted',
      value: stats?.total_comments_deleted || 0,
      icon: CheckCircleIcon,
      color: 'bg-purple-500',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome back, {user?.name}!
        </h1>
        <p className="mt-1 text-sm text-gray-600">
          Here's an overview of your YouTube spam detection activity.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat) => (
          <div key={stat.name} className="card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`p-3 rounded-md ${stat.color}`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {stat.name}
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stat.value.toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Success Rate */}
      {stats && stats.total_spam_detected > 0 && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Deletion Success Rate
          </h3>
          <div className="flex items-center">
            <div className="flex-1">
              <div className="bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full"
                  style={{ width: `${stats.success_rate}%` }}
                />
              </div>
            </div>
            <span className="ml-4 text-sm font-medium text-gray-900">
              {stats.success_rate.toFixed(1)}%
            </span>
          </div>
          <p className="mt-2 text-sm text-gray-600">
            {stats.total_comments_deleted} out of {stats.total_spam_detected} spam comments successfully deleted
          </p>
        </div>
      )}

      {/* Quick Actions */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <Link
            to="/analyze"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <VideoCameraIcon className="h-8 w-8 text-primary-600" />
            <div className="ml-4">
              <h4 className="text-sm font-medium text-gray-900">Analyze Video</h4>
              <p className="text-sm text-gray-600">
                Detect spam comments in a YouTube video
              </p>
            </div>
          </Link>
          <Link
            to="/history"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <ChartBarIcon className="h-8 w-8 text-primary-600" />
            <div className="ml-4">
              <h4 className="text-sm font-medium text-gray-900">View History</h4>
              <p className="text-sm text-gray-600">
                See your past analysis results
              </p>
            </div>
          </Link>
        </div>
      </div>

      {/* Recent Analyses */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Recent Analyses</h3>
          <Link
            to="/history"
            className="text-sm text-primary-600 hover:text-primary-500"
          >
            View all
          </Link>
        </div>
        
        {recentAnalyses.length === 0 ? (
          <div className="text-center py-8">
            <VideoCameraIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No analyses yet</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by analyzing your first video.
            </p>
            <div className="mt-6">
              <Link to="/analyze" className="btn btn-primary">
                Analyze Video
              </Link>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            {recentAnalyses.map((analysis) => (
              <div
                key={analysis.id}
                className="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
              >
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {analysis.video_title || `Video ${analysis.video_id}`}
                  </p>
                  <p className="text-sm text-gray-500">
                    {analysis.total_comments} comments • {analysis.spam_comments_found} spam detected
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <span
                    className={`badge ${
                      analysis.analysis_status === 'completed'
                        ? 'badge-success'
                        : analysis.analysis_status === 'failed'
                        ? 'badge-danger'
                        : 'badge-warning'
                    }`}
                  >
                    {analysis.analysis_status}
                  </span>
                  <span className="text-xs text-gray-500">
                    {new Date(analysis.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
