import React, { useState } from 'react';
import { videoService } from '../services/api';
import { LoadingSpinner } from '../components/LoadingSpinner';
import {
  VideoCameraIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';

interface AnalysisResult {
  id: number;
  video_id: string;
  video_title: string;
  video_url: string;
  channel_name: string;
  total_comments: number;
  spam_comments_found: number;
  comments_deleted: number;
  analysis_status: string;
  dry_run: boolean;
  created_at: string;
}

export const VideoAnalysisPage: React.FC = () => {
  const [videoUrl, setVideoUrl] = useState('');
  const [maxResults, setMaxResults] = useState(100);
  const [confidenceThreshold, setConfidenceThreshold] = useState(0.7);
  const [dryRun, setDryRun] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<AnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const extractVideoId = (url: string): string => {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/,
      /^([a-zA-Z0-9_-]{11})$/,
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) {
        return match[1];
      }
    }

    throw new Error('Invalid YouTube URL or video ID');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const videoId = extractVideoId(videoUrl);
      
      const analysisResult = await videoService.processVideo({
        video_id: videoId,
        max_results: maxResults,
        dry_run: dryRun,
        confidence_threshold: confidenceThreshold,
      });

      setResult(analysisResult);
    } catch (err: any) {
      setError(err.response?.data?.detail || err.message || 'Analysis failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Analyze YouTube Video</h1>
        <p className="mt-1 text-sm text-gray-600">
          Detect and analyze spam comments in YouTube videos using AI-powered detection.
        </p>
      </div>

      {/* Analysis Form */}
      <div className="card">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="video-url" className="block text-sm font-medium text-gray-700">
              YouTube Video URL or ID
            </label>
            <div className="mt-1">
              <input
                type="text"
                id="video-url"
                value={videoUrl}
                onChange={(e) => setVideoUrl(e.target.value)}
                className="input"
                placeholder="https://www.youtube.com/watch?v=dQw4w9WgXcQ or dQw4w9WgXcQ"
                required
              />
            </div>
            <p className="mt-2 text-sm text-gray-500">
              Enter a YouTube video URL or video ID to analyze its comments for spam.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
            <div>
              <label htmlFor="max-results" className="block text-sm font-medium text-gray-700">
                Max Comments
              </label>
              <div className="mt-1">
                <input
                  type="number"
                  id="max-results"
                  value={maxResults}
                  onChange={(e) => setMaxResults(parseInt(e.target.value))}
                  className="input"
                  min="1"
                  max="500"
                />
              </div>
            </div>

            <div>
              <label htmlFor="confidence" className="block text-sm font-medium text-gray-700">
                Confidence Threshold
              </label>
              <div className="mt-1">
                <input
                  type="number"
                  id="confidence"
                  value={confidenceThreshold}
                  onChange={(e) => setConfidenceThreshold(parseFloat(e.target.value))}
                  className="input"
                  min="0.1"
                  max="1.0"
                  step="0.1"
                />
              </div>
            </div>

            <div className="flex items-center">
              <input
                id="dry-run"
                type="checkbox"
                checked={dryRun}
                onChange={(e) => setDryRun(e.target.checked)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="dry-run" className="ml-2 block text-sm text-gray-900">
                Dry Run (No Deletion)
              </label>
            </div>
          </div>

          {!dryRun && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">
                    Comment Deletion Enabled
                  </h3>
                  <p className="mt-2 text-sm text-yellow-700">
                    With dry run disabled, detected spam comments will be permanently deleted.
                    This action can only be performed if you are the owner of the video.
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isLoading}
              className="btn btn-primary flex items-center space-x-2"
            >
              {isLoading ? (
                <>
                  <LoadingSpinner size="sm" />
                  <span>Analyzing...</span>
                </>
              ) : (
                <>
                  <VideoCameraIcon className="h-5 w-5" />
                  <span>Analyze Video</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Analysis Failed</h3>
              <p className="mt-2 text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Results Display */}
      {result && (
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Analysis Results</h3>
            <span
              className={`badge ${
                result.analysis_status === 'completed'
                  ? 'badge-success'
                  : result.analysis_status === 'processing'
                  ? 'badge-warning'
                  : 'badge-danger'
              }`}
            >
              {result.analysis_status}
            </span>
          </div>

          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-700">Video Information</h4>
              <div className="mt-2 space-y-1">
                <p className="text-sm text-gray-900">
                  <strong>Title:</strong> {result.video_title}
                </p>
                <p className="text-sm text-gray-900">
                  <strong>Channel:</strong> {result.channel_name}
                </p>
                <p className="text-sm text-gray-900">
                  <strong>Video ID:</strong> {result.video_id}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <InformationCircleIcon className="h-5 w-5 text-blue-500" />
                  <span className="ml-2 text-sm font-medium text-blue-900">
                    Total Comments
                  </span>
                </div>
                <p className="mt-1 text-2xl font-bold text-blue-900">
                  {result.total_comments}
                </p>
              </div>

              <div className="bg-red-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
                  <span className="ml-2 text-sm font-medium text-red-900">
                    Spam Detected
                  </span>
                </div>
                <p className="mt-1 text-2xl font-bold text-red-900">
                  {result.spam_comments_found}
                </p>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <CheckCircleIcon className="h-5 w-5 text-green-500" />
                  <span className="ml-2 text-sm font-medium text-green-900">
                    Comments Deleted
                  </span>
                </div>
                <p className="mt-1 text-2xl font-bold text-green-900">
                  {result.comments_deleted}
                </p>
              </div>
            </div>

            {result.analysis_status === 'processing' && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                <div className="flex">
                  <InformationCircleIcon className="h-5 w-5 text-yellow-400" />
                  <div className="ml-3">
                    <p className="text-sm text-yellow-700">
                      Analysis is still in progress. Results will be updated automatically.
                      You can check the status in your analysis history.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
