import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { userService } from '../services/api';
import { LoadingSpinner } from '../components/LoadingSpinner';
import {
  UserIcon,
  EnvelopeIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

export const ProfilePage: React.FC = () => {
  const { user, logout } = useAuth();
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [name, setName] = useState(user?.name || '');
  const [updateMessage, setUpdateMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsUpdating(true);
    setError(null);
    setUpdateMessage(null);

    try {
      await userService.updateProfile({ name });
      setUpdateMessage('Profile updated successfully!');
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to update profile');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeleteAccount = async () => {
    setIsDeleting(true);
    setError(null);

    try {
      await userService.deleteAccount();
      logout();
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to delete account');
      setIsDeleting(false);
    }
  };

  const getSubscriptionBadge = (tier: string) => {
    switch (tier) {
      case 'premium':
        return 'badge-warning';
      case 'enterprise':
        return 'badge-info';
      default:
        return 'badge-success';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Profile Settings</h1>
        <p className="mt-1 text-sm text-gray-600">
          Manage your account settings and preferences.
        </p>
      </div>

      {/* Profile Information */}
      <div className="card">
        <div className="flex items-center space-x-4 mb-6">
          {user?.avatar_url ? (
            <img
              className="h-16 w-16 rounded-full"
              src={user.avatar_url}
              alt={user.name}
            />
          ) : (
            <div className="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center">
              <UserIcon className="h-8 w-8 text-gray-500" />
            </div>
          )}
          <div>
            <h2 className="text-xl font-semibold text-gray-900">{user?.name}</h2>
            <p className="text-sm text-gray-600">{user?.email}</p>
            <div className="flex items-center space-x-2 mt-1">
              <span className={`badge ${getSubscriptionBadge(user?.subscription_tier || 'free')}`}>
                {user?.subscription_tier || 'free'}
              </span>
              {user?.is_admin && (
                <span className="badge badge-danger">Admin</span>
              )}
            </div>
          </div>
        </div>

        {/* Update Profile Form */}
        <form onSubmit={handleUpdateProfile} className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              Display Name
            </label>
            <div className="mt-1">
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="input"
                required
              />
            </div>
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email Address
            </label>
            <div className="mt-1 flex items-center space-x-2">
              <EnvelopeIcon className="h-5 w-5 text-gray-400" />
              <span className="text-sm text-gray-900">{user?.email}</span>
              <span className="text-xs text-gray-500">(Cannot be changed)</span>
            </div>
          </div>

          {updateMessage && (
            <div className="bg-green-50 border border-green-200 rounded-md p-3">
              <p className="text-sm text-green-600">{updateMessage}</p>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isUpdating}
              className="btn btn-primary flex items-center space-x-2"
            >
              {isUpdating ? (
                <>
                  <LoadingSpinner size="sm" />
                  <span>Updating...</span>
                </>
              ) : (
                <span>Update Profile</span>
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Account Information */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Account Information</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between py-3 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <ShieldCheckIcon className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-gray-900">Account Status</p>
                <p className="text-sm text-gray-600">Active and verified</p>
              </div>
            </div>
            <span className="badge badge-success">Active</span>
          </div>

          <div className="flex items-center justify-between py-3 border-b border-gray-200">
            <div>
              <p className="text-sm font-medium text-gray-900">Subscription Plan</p>
              <p className="text-sm text-gray-600">
                Current plan: {user?.subscription_tier || 'Free'}
              </p>
            </div>
            <span className={`badge ${getSubscriptionBadge(user?.subscription_tier || 'free')}`}>
              {user?.subscription_tier || 'Free'}
            </span>
          </div>

          <div className="flex items-center justify-between py-3">
            <div>
              <p className="text-sm font-medium text-gray-900">Member Since</p>
              <p className="text-sm text-gray-600">
                Account created via Google OAuth
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Danger Zone */}
      <div className="card border-red-200">
        <h3 className="text-lg font-medium text-red-900 mb-4 flex items-center">
          <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
          Danger Zone
        </h3>
        
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <h4 className="text-sm font-medium text-red-800 mb-2">Delete Account</h4>
          <p className="text-sm text-red-700 mb-4">
            Permanently delete your account and all associated data. This action cannot be undone.
          </p>
          
          {!showDeleteConfirm ? (
            <button
              onClick={() => setShowDeleteConfirm(true)}
              className="btn btn-danger"
            >
              Delete Account
            </button>
          ) : (
            <div className="space-y-3">
              <p className="text-sm font-medium text-red-800">
                Are you sure you want to delete your account? This will permanently remove:
              </p>
              <ul className="text-sm text-red-700 list-disc list-inside space-y-1">
                <li>Your profile and account information</li>
                <li>All video analysis history</li>
                <li>All comment analysis data</li>
                <li>Account statistics and preferences</li>
              </ul>
              <div className="flex space-x-3">
                <button
                  onClick={handleDeleteAccount}
                  disabled={isDeleting}
                  className="btn btn-danger flex items-center space-x-2"
                >
                  {isDeleting ? (
                    <>
                      <LoadingSpinner size="sm" />
                      <span>Deleting...</span>
                    </>
                  ) : (
                    <span>Yes, Delete My Account</span>
                  )}
                </button>
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="btn btn-secondary"
                >
                  Cancel
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
