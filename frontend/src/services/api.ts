import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

export const apiClient = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API service functions
export const authService = {
  googleLogin: async (token: string) => {
    const response = await apiClient.post('/auth/google', { token });
    return response.data;
  },
  
  getCurrentUser: async () => {
    const response = await apiClient.get('/auth/me');
    return response.data;
  },
  
  logout: async () => {
    const response = await apiClient.post('/auth/logout');
    return response.data;
  },
};

export const videoService = {
  processVideo: async (data: {
    video_id: string;
    max_results?: number;
    dry_run?: boolean;
    confidence_threshold?: number;
  }) => {
    const response = await apiClient.post('/process-video', data);
    return response.data;
  },
  
  getVideoInfo: async (video_id: string) => {
    const response = await apiClient.post('/video-info', { video_id });
    return response.data;
  },
  
  getVideoAnalysis: async (analysis_id: number) => {
    const response = await apiClient.get(`/analysis/${analysis_id}`);
    return response.data;
  },
  
  getUserVideoAnalyses: async (skip = 0, limit = 50) => {
    const response = await apiClient.get('/analyses', {
      params: { skip, limit },
    });
    return response.data;
  },
};

export const commentService = {
  analyzeComment: async (data: {
    comment_text: string;
    video_id?: string;
  }) => {
    const response = await apiClient.post('/analyze-comment', data);
    return response.data;
  },
  
  getCommentAnalysis: async (analysis_id: number) => {
    const response = await apiClient.get(`/analysis/${analysis_id}`);
    return response.data;
  },
  
  getUserCommentAnalyses: async (
    video_analysis_id?: number,
    skip = 0,
    limit = 100
  ) => {
    const response = await apiClient.get('/analyses', {
      params: { video_analysis_id, skip, limit },
    });
    return response.data;
  },
  
  getSpamComments: async (
    video_analysis_id?: number,
    confidence_threshold = 0.7,
    skip = 0,
    limit = 100
  ) => {
    const response = await apiClient.get('/spam', {
      params: { video_analysis_id, confidence_threshold, skip, limit },
    });
    return response.data;
  },
};

export const userService = {
  getProfile: async () => {
    const response = await apiClient.get('/users/profile');
    return response.data;
  },
  
  updateProfile: async (data: {
    name?: string;
    avatar_url?: string;
    subscription_tier?: string;
  }) => {
    const response = await apiClient.put('/users/profile', data);
    return response.data;
  },
  
  getUserStats: async () => {
    const response = await apiClient.get('/users/stats');
    return response.data;
  },
  
  deleteAccount: async () => {
    const response = await apiClient.delete('/users/account');
    return response.data;
  },
};
