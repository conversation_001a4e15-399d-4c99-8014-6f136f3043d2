# YouTube Comment Spam Detection Platform

A comprehensive web-based platform for detecting and managing spam comments (especially online gambling ads) from YouTube videos using AI-powered detection algorithms.

## 🎯 Overview

This platform helps content creators and moderators automatically identify and manage spam comments on YouTube videos, with a focus on detecting gambling promotions, financial scams, and phishing attempts.

## ✨ Features

### Core Functionality
- **🤖 AI-Powered Spam Detection**: Advanced NLP algorithms to detect gambling, scam, and phishing comments
- **🔐 Secure Authentication**: Google OAuth integration for secure user access
- **📊 Admin Dashboard**: Comprehensive dashboard with analytics and management tools
- **🎥 YouTube Integration**: Direct integration with YouTube Data API v3
- **🗑️ Comment Management**: Automated comment deletion for video owners
- **📈 Analytics & Reporting**: Detailed analysis history and statistics
- **⚡ Batch Processing**: Analyze multiple videos simultaneously

### Spam Detection Categories
- **🎰 Gambling & Casino**: Detect casino, poker, betting promotions
- **💰 Financial Scams**: Identify get-rich-quick schemes and investment fraud
- **🏦 <PERSON><PERSON>**: Catch predatory lending and payday loan spam
- **🎣 Phishing**: Detect suspicious links and credential harvesting attempts
- **📱 Social Engineering**: Identify contact requests and social media scams

## 🛠️ Technology Stack

### Frontend
- **React.js** with Vite for fast development
- **TypeScript** for type safety
- **Tailwind CSS** + **shadcn/ui** for modern UI components
- **React Router** for navigation
- **Axios** for API communication

### Backend
- **FastAPI** (Python) for high-performance API
- **PostgreSQL** for robust data storage
- **SQLAlchemy** ORM for database management
- **Google OAuth 2.0** for authentication
- **YouTube Data API v3** for video/comment access
- **Alembic** for database migrations

### Infrastructure
- **Docker** containerization for easy deployment
- **Docker Compose** for development environment
- **Environment-based** configuration
- **Production-ready** setup with proper logging

## 📁 Project Structure

```
youtube-spam-detector/
├── frontend/                 # React frontend application
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── pages/          # Application pages
│   │   ├── contexts/       # React contexts (auth, etc.)
│   │   ├── services/       # API service functions
│   │   └── App.tsx         # Main application component
│   ├── public/             # Static assets
│   ├── package.json        # Frontend dependencies
│   └── Dockerfile          # Frontend container config
├── backend/                 # FastAPI backend application
│   ├── routers/            # API route handlers
│   ├── services/           # Business logic services
│   ├── models.py           # Database models
│   ├── schemas.py          # Pydantic schemas
│   ├── database.py         # Database configuration
│   ├── main.py             # FastAPI application
│   ├── requirements.txt    # Python dependencies
│   └── Dockerfile          # Backend container config
├── docker-compose.yml      # Development environment
├── .env.example           # Environment variables template
├── start.sh              # Quick start script
├── test_setup.py         # Setup verification script
└── README.md             # This documentation
```

## 🚀 Quick Start

### Option 1: Using Docker (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd youtube-spam-detector
   ```

2. **Configure environment variables**
   ```bash
   cp .env.example backend/.env
   cp frontend/.env.example frontend/.env
   ```

   Edit `backend/.env` with your API credentials:
   ```env
   YOUTUBE_API_KEY=your_youtube_api_key
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   JWT_SECRET_KEY=your_secure_random_string
   ```

3. **Start the application**
   ```bash
   docker-compose up --build
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

### Option 2: Manual Setup

1. **Backend Setup**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   ```

3. **Database Setup**
   ```bash
   # Install PostgreSQL and create database
   createdb youtube_spam_detector

   # Run migrations
   cd backend
   alembic upgrade head
   ```

4. **Start Services**
   ```bash
   # Terminal 1: Backend
   cd backend
   uvicorn main:app --reload

   # Terminal 2: Frontend
   cd frontend
   npm run dev
   ```

## 🔧 Configuration

### Google Cloud Console Setup

1. **Enable APIs**
   - YouTube Data API v3
   - Google+ API (for OAuth)

2. **Create OAuth 2.0 Credentials**
   - Application type: Web application
   - Authorized redirect URIs:
     - `http://localhost:3000`
     - `http://localhost:8000/auth/google/callback`

3. **Get API Keys**
   - YouTube Data API key
   - OAuth 2.0 client ID and secret

### Environment Variables

#### Backend (.env)
```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/youtube_spam_detector

# YouTube API
YOUTUBE_API_KEY=your_youtube_api_key_here

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# JWT
JWT_SECRET_KEY=your_super_secret_jwt_key_here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application
ENVIRONMENT=development
```

#### Frontend (.env)
```env
VITE_API_URL=http://localhost:8000
VITE_GOOGLE_CLIENT_ID=your_google_client_id_here
```

## 📡 API Documentation

### Authentication Endpoints
- `POST /api/auth/google` - Authenticate with Google OAuth
- `GET /api/auth/me` - Get current user information
- `POST /api/auth/logout` - Logout user

### Video Analysis Endpoints
- `POST /api/process-video` - Analyze video comments for spam
- `POST /api/video-info` - Get video metadata
- `GET /api/analysis/{id}` - Get specific analysis results
- `GET /api/analyses` - Get user's analysis history

### Comment Analysis Endpoints
- `POST /api/analyze-comment` - Analyze single comment
- `GET /api/analyses` - Get comment analysis history
- `GET /api/spam` - Get detected spam comments

### User Management Endpoints
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `GET /api/users/stats` - Get user statistics
- `DELETE /api/users/account` - Delete user account

### Sample API Request

```bash
curl -X POST "http://localhost:8000/api/process-video" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "video_id": "dQw4w9WgXcQ",
    "max_results": 100,
    "dry_run": true,
    "confidence_threshold": 0.7
  }'
```

### Sample API Response

```json
{
  "id": 1,
  "video_id": "dQw4w9WgXcQ",
  "video_title": "Rick Astley - Never Gonna Give You Up",
  "channel_name": "Rick Astley",
  "total_comments": 100,
  "spam_comments_found": 5,
  "comments_deleted": 0,
  "analysis_status": "completed",
  "dry_run": true,
  "created_at": "2025-06-22T15:30:00Z"
}
```

## 🧪 Testing

### Run Backend Tests
```bash
cd backend
pytest tests/ -v
```

### Run Frontend Tests
```bash
cd frontend
npm test
```

### Test Coverage
```bash
cd backend
pytest --cov=. tests/
```

### Setup Verification
```bash
python test_setup.py
```

## 🔍 Spam Detection Algorithm

The platform uses a multi-layered approach for spam detection:

### 1. Keyword Matching
- **Gambling terms**: casino, poker, betting, slots
- **Financial scams**: get rich quick, guaranteed returns
- **Loan scams**: instant approval, no credit check
- **Phishing**: click here, limited time, urgent action

### 2. Pattern Recognition
- **URLs and links**: Suspicious shortened URLs
- **Contact information**: Social media handles, phone numbers
- **Character patterns**: Excessive caps, repeated characters
- **Emoji analysis**: Gambling and money-related emojis

### 3. Advanced Detection
- **Text preprocessing**: Unicode normalization, character substitution
- **Social engineering**: Direct message requests, urgency tactics
- **Behavioral analysis**: Comment frequency, account age
- **Context awareness**: Video topic relevance

### 4. Confidence Scoring
- **Weighted scoring**: Different categories have different weights
- **Threshold-based**: Configurable confidence thresholds
- **False positive reduction**: Multiple validation layers

## 📊 Features in Detail

### Dashboard Analytics
- **Overview statistics**: Videos analyzed, spam detected, success rate
- **Recent activity**: Latest analysis results
- **Performance metrics**: Detection accuracy, processing time
- **Trend analysis**: Spam patterns over time

### Video Analysis
- **Bulk processing**: Analyze multiple videos simultaneously
- **Real-time results**: Live updates during analysis
- **Detailed reporting**: Per-comment breakdown with confidence scores
- **Export functionality**: Download results in various formats

### Comment Management
- **Automated deletion**: Remove spam comments automatically (video owners only)
- **Manual review**: Flag suspicious comments for manual review
- **Whitelist/Blacklist**: Custom keyword management
- **Bulk actions**: Process multiple comments at once

## 🚨 Limitations & Considerations

### API Rate Limits
- YouTube Data API has daily quotas
- Implement proper rate limiting and caching
- Consider API costs for high-volume usage

### Detection Accuracy
- AI models may have false positives/negatives
- Regular model updates and training required
- Human review recommended for critical decisions

### Legal & Ethical
- Respect user privacy and data protection laws
- Follow YouTube's Terms of Service
- Obtain proper permissions for comment deletion

## 🛠️ Development

### Adding New Spam Categories
1. Update `spam_detector.py` with new keywords
2. Add category to database schema
3. Update frontend UI for new category
4. Write tests for new detection logic

### Database Migrations
```bash
cd backend
alembic revision --autogenerate -m "Description"
alembic upgrade head
```

### Code Style
- Backend: Follow PEP 8, use Black formatter
- Frontend: Use Prettier, ESLint configuration
- Commit messages: Follow conventional commits

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check this README and API docs at `/docs`
- **Issues**: Report bugs and feature requests on GitHub
- **Discussions**: Join community discussions for help and ideas

## 🙏 Acknowledgments

- YouTube Data API v3 for video and comment access
- Google OAuth for secure authentication
- FastAPI and React communities for excellent frameworks
- Open source NLP libraries for text analysis capabilities
