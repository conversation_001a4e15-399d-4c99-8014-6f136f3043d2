#!/usr/bin/env python3
"""
Test script to verify the YouTube Spam Detection Platform setup
"""

import os
import sys
import subprocess
import requests
import time
from pathlib import Path

def check_file_exists(file_path):
    """Check if a file exists"""
    if Path(file_path).exists():
        print(f"✅ {file_path} exists")
        return True
    else:
        print(f"❌ {file_path} missing")
        return False

def check_directory_structure():
    """Check if all required directories and files exist"""
    print("🔍 Checking project structure...")
    
    required_files = [
        "README.md",
        ".env.example",
        "docker-compose.yml",
        "backend/main.py",
        "backend/requirements.txt",
        "backend/Dockerfile",
        "backend/config.py",
        "backend/database.py",
        "backend/models.py",
        "backend/schemas.py",
        "backend/routers/auth.py",
        "backend/routers/videos.py",
        "backend/routers/comments.py",
        "backend/routers/users.py",
        "backend/services/youtube_service.py",
        "backend/services/spam_detector.py",
        "frontend/package.json",
        "frontend/Dockerfile",
        "frontend/tailwind.config.js",
        "frontend/postcss.config.js",
        "frontend/src/App.tsx",
        "frontend/src/contexts/AuthContext.tsx",
        "frontend/src/services/api.ts",
        "frontend/src/components/Layout.tsx",
        "frontend/src/components/ProtectedRoute.tsx",
        "frontend/src/components/LoadingSpinner.tsx",
        "frontend/src/pages/LoginPage.tsx",
        "frontend/src/pages/DashboardPage.tsx",
        "frontend/src/pages/VideoAnalysisPage.tsx",
        "frontend/src/pages/HistoryPage.tsx",
        "frontend/src/pages/ProfilePage.tsx",
    ]
    
    all_exist = True
    for file_path in required_files:
        if not check_file_exists(file_path):
            all_exist = False
    
    return all_exist

def check_backend_dependencies():
    """Check if backend dependencies can be imported"""
    print("\n🔍 Checking backend dependencies...")
    
    try:
        import fastapi
        print("✅ FastAPI installed")
    except ImportError:
        print("❌ FastAPI not installed")
        return False
    
    try:
        import sqlalchemy
        print("✅ SQLAlchemy installed")
    except ImportError:
        print("❌ SQLAlchemy not installed")
        return False
    
    try:
        import google.auth
        print("✅ Google Auth libraries installed")
    except ImportError:
        print("❌ Google Auth libraries not installed")
        return False
    
    return True

def check_frontend_dependencies():
    """Check if frontend dependencies are installed"""
    print("\n🔍 Checking frontend dependencies...")
    
    if not Path("frontend/node_modules").exists():
        print("❌ Frontend dependencies not installed")
        print("💡 Run: cd frontend && npm install")
        return False
    
    print("✅ Frontend dependencies installed")
    return True

def test_backend_startup():
    """Test if backend can start"""
    print("\n🔍 Testing backend startup...")
    
    try:
        # Change to backend directory
        os.chdir("backend")
        
        # Try to import main module
        sys.path.insert(0, ".")
        import main
        print("✅ Backend main module can be imported")
        
        # Change back to root directory
        os.chdir("..")
        return True
        
    except Exception as e:
        print(f"❌ Backend startup failed: {e}")
        os.chdir("..")
        return False

def test_spam_detector():
    """Test the spam detection functionality"""
    print("\n🔍 Testing spam detection...")

    try:
        sys.path.insert(0, "backend")
        from services.spam_detector import SpamDetector
        import asyncio

        detector = SpamDetector()

        # Test with obvious spam
        spam_text = "Win big at our casino! Easy money guaranteed! Click here now!"

        # Run async function properly
        async def run_test():
            return await detector.analyze_comment(spam_text)

        result = asyncio.run(run_test())

        if result['is_spam'] and result['confidence'] > 0.5:
            print("✅ Spam detection working correctly")
            print(f"   Detected spam with {result['confidence']:.2f} confidence")
            print(f"   Categories: {result['categories']}")
            return True
        else:
            print("❌ Spam detection not working properly")
            return False

    except Exception as e:
        print(f"❌ Spam detection test failed: {e}")
        return False

def create_env_file():
    """Create .env file from example if it doesn't exist"""
    print("\n🔧 Setting up environment files...")
    
    # Backend .env
    if not Path("backend/.env").exists():
        if Path(".env.example").exists():
            subprocess.run(["cp", ".env.example", "backend/.env"])
            print("✅ Created backend/.env from .env.example")
        else:
            print("❌ .env.example not found")
            return False
    
    # Frontend .env
    if not Path("frontend/.env").exists():
        if Path("frontend/.env.example").exists():
            subprocess.run(["cp", "frontend/.env.example", "frontend/.env"])
            print("✅ Created frontend/.env from frontend/.env.example")
        else:
            print("❌ frontend/.env.example not found")
            return False
    
    return True

def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "="*60)
    print("🎉 SETUP VERIFICATION COMPLETE!")
    print("="*60)
    
    print("\n📋 NEXT STEPS:")
    print("\n1. Configure Environment Variables:")
    print("   - Edit backend/.env with your API keys:")
    print("     • YOUTUBE_API_KEY (from Google Cloud Console)")
    print("     • GOOGLE_CLIENT_ID (from Google Cloud Console)")
    print("     • GOOGLE_CLIENT_SECRET (from Google Cloud Console)")
    print("     • JWT_SECRET_KEY (generate a secure random string)")
    
    print("\n2. Start the Application:")
    print("   Option A - Using Docker (Recommended):")
    print("     docker-compose up")
    print("\n   Option B - Manual startup:")
    print("     Terminal 1: cd backend && uvicorn main:app --reload")
    print("     Terminal 2: cd frontend && npm run dev")
    
    print("\n3. Access the Application:")
    print("   • Frontend: http://localhost:3000")
    print("   • Backend API: http://localhost:8000")
    print("   • API Documentation: http://localhost:8000/docs")
    
    print("\n4. Google Cloud Console Setup:")
    print("   • Enable YouTube Data API v3")
    print("   • Create OAuth 2.0 credentials")
    print("   • Add authorized redirect URIs:")
    print("     - http://localhost:3000")
    print("     - http://localhost:8000/auth/google/callback")
    
    print("\n5. Test the Application:")
    print("   • Sign in with Google")
    print("   • Analyze a YouTube video")
    print("   • Check the analysis results")
    
    print("\n📚 Documentation:")
    print("   • README.md - Project overview")
    print("   • API docs at /docs endpoint")
    print("   • Environment variables in .env.example")

def main():
    """Main test function"""
    print("🚀 YouTube Spam Detection Platform - Setup Verification")
    print("="*60)
    
    all_checks_passed = True
    
    # Check project structure
    if not check_directory_structure():
        all_checks_passed = False
    
    # Create environment files
    if not create_env_file():
        all_checks_passed = False
    
    # Check backend dependencies (only if in backend environment)
    if Path("backend/requirements.txt").exists():
        try:
            os.chdir("backend")
            if not check_backend_dependencies():
                all_checks_passed = False
            os.chdir("..")
        except:
            print("⚠️  Backend dependencies check skipped (run from backend directory)")
    
    # Check frontend dependencies
    if not check_frontend_dependencies():
        all_checks_passed = False
    
    # Test backend startup
    if not test_backend_startup():
        all_checks_passed = False
    
    # Test spam detector
    if not test_spam_detector():
        all_checks_passed = False
    
    if all_checks_passed:
        print("\n✅ All checks passed!")
        print_next_steps()
    else:
        print("\n❌ Some checks failed. Please fix the issues above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
