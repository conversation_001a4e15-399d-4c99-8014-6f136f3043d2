#!/bin/bash

# YouTube Spam Detection Platform - Startup Script

set -e

echo "🚀 Starting YouTube Spam Detection Platform..."
echo "================================================"

# Check if Docker is available
if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
    echo "🐳 Docker detected. Starting with Docker Compose..."
    
    # Check if .env files exist
    if [ ! -f "backend/.env" ]; then
        echo "📝 Creating backend/.env from .env.example..."
        cp .env.example backend/.env
    fi
    
    if [ ! -f "frontend/.env" ]; then
        echo "📝 Creating frontend/.env from frontend/.env.example..."
        cp frontend/.env.example frontend/.env
    fi
    
    echo "⚠️  Please configure your API keys in backend/.env before proceeding!"
    echo "   Required: YOUTUBE_API_KEY, GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET"
    echo ""
    read -p "Press Enter to continue when you've configured the environment variables..."
    
    # Start with Docker Compose
    docker-compose up --build
    
else
    echo "🔧 Docker not found. Starting manually..."
    
    # Check if backend dependencies are installed
    if [ ! -d "backend/venv" ] && [ ! -f "backend/.venv" ]; then
        echo "📦 Setting up Python virtual environment..."
        cd backend
        python3 -m venv venv
        source venv/bin/activate
        pip install -r requirements.txt
        cd ..
    fi
    
    # Check if frontend dependencies are installed
    if [ ! -d "frontend/node_modules" ]; then
        echo "📦 Installing frontend dependencies..."
        cd frontend
        npm install
        cd ..
    fi
    
    # Create .env files if they don't exist
    if [ ! -f "backend/.env" ]; then
        echo "📝 Creating backend/.env from .env.example..."
        cp .env.example backend/.env
    fi
    
    if [ ! -f "frontend/.env" ]; then
        echo "📝 Creating frontend/.env from frontend/.env.example..."
        cp frontend/.env.example frontend/.env
    fi
    
    echo "⚠️  Please configure your API keys in backend/.env!"
    echo "   Required: YOUTUBE_API_KEY, GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET"
    echo ""
    read -p "Press Enter to continue when you've configured the environment variables..."
    
    # Start PostgreSQL (if not running)
    if ! pgrep -x "postgres" > /dev/null; then
        echo "🐘 Starting PostgreSQL..."
        # This will vary by system - adjust as needed
        if command -v brew &> /dev/null; then
            brew services start postgresql
        elif command -v systemctl &> /dev/null; then
            sudo systemctl start postgresql
        else
            echo "⚠️  Please start PostgreSQL manually"
        fi
    fi
    
    echo "🚀 Starting backend and frontend..."
    
    # Start backend in background
    cd backend
    source venv/bin/activate
    uvicorn main:app --reload --host 0.0.0.0 --port 8000 &
    BACKEND_PID=$!
    cd ..
    
    # Start frontend in background
    cd frontend
    npm run dev -- --host 0.0.0.0 --port 3000 &
    FRONTEND_PID=$!
    cd ..
    
    echo "✅ Application started!"
    echo "   Frontend: http://localhost:3000"
    echo "   Backend: http://localhost:8000"
    echo "   API Docs: http://localhost:8000/docs"
    echo ""
    echo "Press Ctrl+C to stop the application"
    
    # Wait for interrupt
    trap "echo 'Stopping...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
    wait
fi
