# Deployment Guide

This guide covers deploying the YouTube Comment Spam Detection Platform to production environments.

## 🚀 Production Deployment Options

### Option 1: Docker Compose (Recommended for small-medium scale)

#### Prerequisites
- <PERSON>er and <PERSON>er Compose installed
- Domain name configured
- SSL certificate (Let's Encrypt recommended)

#### Steps

1. **<PERSON><PERSON> and Configure**
   ```bash
   git clone <repository-url>
   cd youtube-spam-detector
   cp .env.example .env
   ```

2. **Update Environment Variables**
   ```bash
   # Edit .env with production values
   DATABASE_URL=****************************************/youtube_spam_detector
   YOUTUBE_API_KEY=your_production_api_key
   GOOGLE_CLIENT_ID=your_production_client_id
   GOOGLE_CLIENT_SECRET=your_production_client_secret
   JWT_SECRET_KEY=your_super_secure_random_string
   ENVIRONMENT=production
   FRONTEND_URL=https://yourdomain.com
   BACKEND_URL=https://api.yourdomain.com
   ```

3. **Production Docker Compose**
   ```yaml
   # docker-compose.prod.yml
   version: '3.8'
   services:
     postgres:
       image: postgres:15-alpine
       environment:
         POSTGRES_DB: youtube_spam_detector
         POSTGRES_USER: ${DATABASE_USER}
         POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
       volumes:
         - postgres_data:/var/lib/postgresql/data
       restart: unless-stopped

     backend:
       build: ./backend
       environment:
         - DATABASE_URL=${DATABASE_URL}
         - YOUTUBE_API_KEY=${YOUTUBE_API_KEY}
         - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
         - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
         - JWT_SECRET_KEY=${JWT_SECRET_KEY}
         - ENVIRONMENT=production
       depends_on:
         - postgres
       restart: unless-stopped

     frontend:
       build: ./frontend
       environment:
         - VITE_API_URL=${BACKEND_URL}
         - VITE_GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
       restart: unless-stopped

     nginx:
       image: nginx:alpine
       ports:
         - "80:80"
         - "443:443"
       volumes:
         - ./nginx.conf:/etc/nginx/nginx.conf
         - ./ssl:/etc/nginx/ssl
       depends_on:
         - frontend
         - backend
       restart: unless-stopped

   volumes:
     postgres_data:
   ```

4. **Deploy**
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

### Option 2: Cloud Platform Deployment

#### Google Cloud Platform

1. **Setup GCP Project**
   ```bash
   gcloud projects create youtube-spam-detector
   gcloud config set project youtube-spam-detector
   ```

2. **Enable Required APIs**
   ```bash
   gcloud services enable cloudbuild.googleapis.com
   gcloud services enable run.googleapis.com
   gcloud services enable sqladmin.googleapis.com
   ```

3. **Deploy Database**
   ```bash
   gcloud sql instances create youtube-spam-db \
     --database-version=POSTGRES_14 \
     --tier=db-f1-micro \
     --region=us-central1
   ```

4. **Deploy Backend to Cloud Run**
   ```bash
   cd backend
   gcloud builds submit --tag gcr.io/youtube-spam-detector/backend
   gcloud run deploy backend \
     --image gcr.io/youtube-spam-detector/backend \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated
   ```

5. **Deploy Frontend**
   ```bash
   cd frontend
   npm run build
   gsutil -m rsync -r -d dist gs://youtube-spam-detector-frontend
   ```

#### AWS Deployment

1. **Setup ECS/Fargate**
   - Create ECS cluster
   - Define task definitions for backend and frontend
   - Setup Application Load Balancer
   - Configure RDS PostgreSQL instance

2. **Deploy with CDK/CloudFormation**
   ```typescript
   // Example CDK stack
   const cluster = new ecs.Cluster(this, 'Cluster', { vpc });
   const taskDefinition = new ecs.FargateTaskDefinition(this, 'TaskDef');
   // Add container definitions...
   ```

## 🔒 Security Considerations

### Environment Variables
- Use secrets management (AWS Secrets Manager, GCP Secret Manager)
- Never commit sensitive data to version control
- Rotate API keys and secrets regularly

### Database Security
- Enable SSL connections
- Use strong passwords
- Implement connection pooling
- Regular backups and point-in-time recovery

### API Security
- Implement rate limiting
- Use HTTPS everywhere
- Validate all inputs
- Monitor for suspicious activity

### Authentication
- Secure JWT secret keys
- Implement token refresh
- Monitor authentication attempts
- Use secure session management

## 📊 Monitoring and Logging

### Application Monitoring
```python
# Add to main.py
import logging
from prometheus_fastapi_instrumentator import Instrumentator

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add metrics
Instrumentator().instrument(app).expose(app)
```

### Database Monitoring
- Monitor connection pools
- Track query performance
- Set up alerts for high CPU/memory usage
- Regular maintenance and optimization

### Log Aggregation
- Use structured logging (JSON format)
- Centralized log collection (ELK stack, Fluentd)
- Set up alerts for errors and anomalies

## 🔧 Performance Optimization

### Backend Optimization
- Implement Redis caching
- Use connection pooling
- Optimize database queries
- Implement background tasks with Celery

### Frontend Optimization
- Code splitting and lazy loading
- CDN for static assets
- Image optimization
- Bundle size optimization

### Database Optimization
- Add appropriate indexes
- Implement query optimization
- Use read replicas for scaling
- Regular VACUUM and ANALYZE

## 📈 Scaling Strategies

### Horizontal Scaling
- Load balancer configuration
- Multiple backend instances
- Database read replicas
- CDN for static content

### Vertical Scaling
- Increase server resources
- Optimize application performance
- Database performance tuning

### Caching Strategy
```python
# Redis caching example
import redis
from functools import wraps

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_result(expiration=3600):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            cached = redis_client.get(cache_key)
            if cached:
                return json.loads(cached)
            result = await func(*args, **kwargs)
            redis_client.setex(cache_key, expiration, json.dumps(result))
            return result
        return wrapper
    return decorator
```

## 🚨 Backup and Recovery

### Database Backups
```bash
# Automated backup script
#!/bin/bash
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump youtube_spam_detector > "$BACKUP_DIR/backup_$DATE.sql"
```

### Application Backups
- Code repository (Git)
- Configuration files
- SSL certificates
- Log files

### Disaster Recovery
- Document recovery procedures
- Test backup restoration regularly
- Implement monitoring and alerting
- Maintain offsite backups

## 📋 Deployment Checklist

### Pre-deployment
- [ ] Environment variables configured
- [ ] SSL certificates installed
- [ ] Database migrations tested
- [ ] API keys and secrets secured
- [ ] Monitoring and logging setup

### Deployment
- [ ] Build and test all components
- [ ] Deploy database first
- [ ] Deploy backend services
- [ ] Deploy frontend
- [ ] Configure load balancer/proxy
- [ ] Test all endpoints

### Post-deployment
- [ ] Verify all services running
- [ ] Check logs for errors
- [ ] Test critical user flows
- [ ] Monitor performance metrics
- [ ] Setup alerts and notifications

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run tests
        run: |
          cd backend && pytest
          cd frontend && npm test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to production
        run: |
          # Deployment commands
```

## 📞 Support and Maintenance

### Regular Maintenance
- Update dependencies regularly
- Monitor security vulnerabilities
- Performance optimization
- Database maintenance

### Troubleshooting
- Check application logs
- Monitor system resources
- Verify API connectivity
- Test database connections

### Emergency Procedures
- Incident response plan
- Rollback procedures
- Emergency contacts
- Status page updates
